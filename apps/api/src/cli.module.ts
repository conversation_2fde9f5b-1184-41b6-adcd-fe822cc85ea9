import { Commands } from '@/commands';
import { AdminModule } from '@modules/admin/admin.module';
import { AuthModule } from '@modules/auth/auth.module';
import { AppCacheModule } from '@modules/cache/cache.module';
import { ChatModule } from '@modules/chat/chat.module';
import { ContextModule } from '@modules/context/context.module';
import { ContextPopulationService } from '@modules/context/context.service';
import { DoctorModule } from '@modules/doctor/doctor.module';
import { DosespotModule } from '@modules/dosespot/dosespot.module';
import { IntegrationsModule } from '@modules/integrations/integrations.module';
import { OnboardingModule } from '@modules/onboarding/onboarding.module';
import { PatientModule } from '@modules/patient/patient.module';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { ProductModule } from '@modules/product/product.module';
import { ReferralModule } from '@modules/referral/referral.module';
import { SegmentModule } from '@modules/segment/segment.module';
import { AwsModule } from '@modules/shared/aws/aws.module';
import { OrchestrationModule } from '@modules/shared/orchestration/orchestration.module';
import { QueueModule } from '@modules/shared/queue/queue.module';
import { SharedModule } from '@modules/shared/shared.module';
import { StripeModule } from '@modules/stripe/stripe.module';
import { TreatmentModule } from '@modules/treatment/treatment.module';
import { Module, OnModuleInit } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';

@Module({
  imports: [
    PrismaModule,
    EventEmitterModule.forRoot(),
    ConfigModule.forRoot({
      envFilePath: process.env.ENV_FILE || '.env',
      isGlobal: true,
    }),
    QueueModule.forRoot({
      postgres: {
        url: process.env.DATABASE_URL!,
      },
    }),
    ContextModule.forCli(),
    ChatModule,
    AppCacheModule,
    AuthModule,
    OnboardingModule,
    PatientModule,
    DoctorModule,
    SharedModule,
    StripeModule,
    DosespotModule,
    IntegrationsModule,
    SegmentModule,
    TreatmentModule,
    ReferralModule,
    ProductModule,
    AdminModule,
    AwsModule,
    OrchestrationModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
      },
    }),
  ],
  providers: [...Commands],
})
export class CliModule implements OnModuleInit {
  constructor(
    readonly configService: ConfigService,
    private readonly contextPopulate: ContextPopulationService,
  ) {
    // Set IS_CLI to true to disable all workers
    this.configService.set('IS_CLI', 'true');

    // These are kept for backward compatibility
    this.configService.set('ENABLE_SNS_CONSUMER', 'false');
    this.configService.set('ENABLE_PGBOSS_CONSUMER', 'false');
    this.configService.set('ENABLE_SQS_CONSUMER', 'false');
  }

  onModuleInit() {
    // this.contextPopulate.populateActor_Command();
  }
}
