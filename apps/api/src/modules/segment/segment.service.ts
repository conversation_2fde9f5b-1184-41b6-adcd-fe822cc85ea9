import { ExecutionContext } from '@/helpers/transaction';
import { SegmentIdentify, SegmentTrack } from '@modules/shared/types/events';
import { Injectable } from '@nestjs/common';
import { addSeconds } from 'date-fns';

import { SegmentOutboxerQueue } from './segment-outboxer.queue';
import { SegmentTrackEventName } from './segment.definitions';

@Injectable()
export class SegmentService {
  constructor(private readonly segmentOutboxer: SegmentOutboxerQueue) {}

  async track<TEventName extends SegmentTrackEventName>(
    patientId: string,
    event: TEventName,
    data: Omit<SegmentTrack, 'event' | 'userId' | 'anonymousId'>,
    {
      startAfterSeconds,
      ...ctx
    }: ExecutionContext & { startAfterSeconds?: number } = {},
  ) {
    return this.segmentOutboxer.send(
      patientId,
      {
        type: 'track',
        event,
        data,
      },
      {
        ...ctx,
        priority: 0,
        startAfter: addSeconds(new Date(), startAfterSeconds ?? 20),
      },
    );
  }

  async identify(
    patientId: string,
    data: Omit<SegmentIdentify, 'userId' | 'anonymousId'>,
    ctx: ExecutionContext = {},
  ) {
    return this.segmentOutboxer.send(
      patientId,
      {
        type: 'identify',
        data,
      },
      { ...ctx, priority: 1 },
    );
  }
}
