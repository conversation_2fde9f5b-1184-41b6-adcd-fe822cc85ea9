import { Module } from '@nestjs/common';

import { PrismaModule } from '../prisma/prisma.module';
import { SegmentOutboxerQueue } from './segment-outboxer.queue';
import { SegmentAdapter } from './segment.adapter';
import { SegmentService } from './segment.service';

@Module({
  imports: [PrismaModule],
  providers: [SegmentAdapter, SegmentService, SegmentOutboxerQueue],
  exports: [SegmentService],
})
export class SegmentModule {}
