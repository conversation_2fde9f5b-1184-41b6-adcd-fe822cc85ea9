'use client';

import { useRef } from 'react';
import { datadogLogs } from '@datadog/browser-logs';
import { isAxiosError } from 'axios';

import { serializeAxiosError } from '@willow/utils/axios';

import { env } from '~/env';
import { useGetDoctorProfile } from './doctor';

type LogSeverity = 'info' | 'error' | 'warn' | 'debug';

interface LogPayload {
  severity?: LogSeverity;
  event: string;
  aggregateId?: string;
  traceId?: string;
  error?: Error;
  [key: string]: unknown;
}

export function useLog() {
  const { data: profile } = useGetDoctorProfile();

  const logRef = useRef(
    ({ event, aggregateId, severity, error, ...payload }: LogPayload) => {
      if (env.NEXT_PUBLIC_ENVIRONMENT === 'development') {
        console.log(
          `[${severity}] Logging: ${aggregateId ? `${aggregateId}->` : ''}${event}`,
          payload,
        );
        return;
      }

      let _error = error;
      if (isAxiosError(error)) {
        _error = serializeAxiosError(error) as unknown as Error;
      }

      void datadogLogs.logger.log(
        event,
        {
          source: 'doctor-dashboard-logs',
          session: profile ? { email: profile?.user.email } : undefined,
          aggregateId,
          app: 'doctor-dashboard',

          ...payload,
        },
        severity ?? 'info',
        _error,
      );
    },
  );

  return {
    genRandomTraceId: () => Math.random().toString(36).substring(2, 15),
    log: logRef.current,
    initTracedLog: (_tracingId?: string) => {
      const tracingId =
        _tracingId ?? Math.random().toString(36).substring(2, 15);
      return (data: LogPayload) => logRef.current({ tracingId, ...data });
    },
    isPending: false,
  };
}
