import { fileURLToPath } from 'url';
import create<PERSON>iti from 'jiti';

// Import env files to validate at build time. Use jiti so we can load .ts files in here.
await create<PERSON>iti(fileURLToPath(import.meta.url))('./src/env');

/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: [
    'jotai-devtools',
    '@willow/ui',
    '@willow/tailwind-config',
    '@willow/utils',
    '@willow/auth',
  ],

  /** We already do linting and typechecking as separate tasks in CI */
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },

  images: {
    remotePatterns: [
      { protocol: 'https', hostname: 'local-data.startwillow.com' },
      { protocol: 'https', hostname: 'staging-data.startwillow.com' },
      { protocol: 'https', hostname: 'data.startwillow.com' },
      { protocol: 'https', hostname: 'local-products.startwillow.com' },
      { protocol: 'https', hostname: 'staging-products.startwillow.com' },
      { protocol: 'https', hostname: 'production-products.startwillow.com' },
    ],
  },

  async rewrites() {
    return [
      {
        source: '/dd/:path*/foo',
        destination: 'https://browser-intake-us5-datadoghq.com/:path*',
      },
      {
        source: '/mp/lib.min.js',
        destination: 'https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js',
      },
      {
        source: '/mp/lib.js',
        destination: 'https://cdn.mxpnl.com/libs/mixpanel-2-latest.js',
      },
      {
        source: '/mp/decide',
        destination: 'https://decide.mixpanel.com/decide',
      },
      {
        source: '/mp/:slug',
        destination: 'https://api.mixpanel.com/:slug',
      },
    ];
  },

  async redirects() {
    return [
      {
        source: '/account',
        destination: '/account/state',
        permanent: true,
      },
    ];
  },
};

export default nextConfig;
