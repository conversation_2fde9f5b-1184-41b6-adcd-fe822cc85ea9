'use client';

import type {
  StripeCardCvcElement,
  StripeCardExpiryElement,
  StripeCardNumberElement,
  StripeCardNumberElementChangeEvent,
} from '@stripe/stripe-js';
import type React from 'react';
import { useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Amex } from '@/assets/svg/credit-card-logos/Amex';
import { Discover } from '@/assets/svg/credit-card-logos/Discover';
import { FSA } from '@/assets/svg/credit-card-logos/Fsa';
import { HSA } from '@/assets/svg/credit-card-logos/Hsa';
import { JCB } from '@/assets/svg/credit-card-logos/JCB';
import { Mastercard } from '@/assets/svg/credit-card-logos/Mastercard';
import { Visa } from '@/assets/svg/credit-card-logos/Visa';
import { useUpdateBilling } from '@/hooks/onboarding';
import { useStates } from '@/hooks/useStates';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
  useElements,
  useStripe,
} from '@stripe/react-stripe-js';
import { CircleArrowRight } from 'lucide-react';
import { useForm } from 'react-hook-form';

import { Button } from '@willow/ui/base/button';
import { Checkbox } from '@willow/ui/base/checkbox';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  Form as ReactForm,
} from '@willow/ui/base/form';
import { Input } from '@willow/ui/base/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@willow/ui/base/select';
import { Loader } from '@willow/ui/loader';

import type { ElementStatus, FormKeys, FormType } from './_FormUtils';
import type { BillingAddress, ShippingAddress } from '~/data/types';
import Logo from '~/assets/svg/logo-denim.svg';
import { useProfile, useRefreshProfile } from '~/hooks/profile';
import { useLog } from '~/hooks/useLog';
import { renderFormMessage, schema, stripeInputStyle } from './_FormUtils';

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export const BillingForm = ({
  clientSecret,
  billingAddress,
  shippingAddress,
}: {
  clientSecret: string;
  billingAddress: BillingAddress;
  shippingAddress: ShippingAddress;
}) => {
  const { initTracedLog } = useLog();
  const stripe = useStripe();
  const elements = useElements();
  const { data: states } = useStates();
  const profile = useProfile();
  const refreshProfile = useRefreshProfile();

  const { mutateAsync: updateBillingAddress } = useUpdateBilling('dashboard');

  const [formError, setFormError] = useState<string | undefined>(undefined);
  const [isEditing, setIsEditing] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isRenderSuccessMessage, setIsRenderSuccessMessage] = useState(false);

  const isDefaultBillingSameAsShipping =
    billingAddress.line1 === shippingAddress.address1 &&
    billingAddress.city === shippingAddress.city &&
    billingAddress.postal_code === shippingAddress.zip
      ? true
      : false;

  const [cardStatus, setCardStatus] = useState<ElementStatus>({
    name: 'card',
    error: undefined,
    complete: false,
  });
  const [expiryStatus, setExpiryStatus] = useState<ElementStatus>({
    name: 'expiry',
    error: undefined,
    complete: false,
  });
  const [cvcStatus, setCvcStatus] = useState<ElementStatus>({
    name: 'cvc',
    error: undefined,
    complete: false,
  });

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      billingSameAsShipping: isDefaultBillingSameAsShipping,
      address1: billingAddress.line1 ?? undefined,
      address2: billingAddress.line2 ?? undefined,
      city: billingAddress.city ?? undefined,
      state: billingAddress.state ?? undefined,
      zip: billingAddress.postal_code ?? undefined,
    },
  });

  if (!elements || !states)
    return <Loader className="min-h-[465px] rounded-2xl bg-stone-light" />;

  const cardNumber = elements.getElement(CardNumberElement) ?? null;

  const onStripeElementReady = (
    errorKey: FormKeys,
    element:
      | StripeCardNumberElement
      | StripeCardCvcElement
      | StripeCardExpiryElement,
    setter: React.Dispatch<React.SetStateAction<ElementStatus>>,
  ) => {
    (element as StripeCardNumberElement).on(
      'change',
      (e: StripeCardNumberElementChangeEvent) => {
        if (e.error) {
          form.setError(errorKey, { message: e.error.message });
        }
        if (!e.error) {
          form.clearErrors(errorKey);
        }
        setter((prev) => ({
          ...prev,
          error: e.error,
          complete: e.complete,
        }));
      },
    );
  };

  const onSubmit = async (data: FormType) => {
    const log = initTracedLog();

    log({
      aggregateId: 'update-billing',
      event: 'on-submit-trigger',
      data,
    });

    setFormError(undefined);
    if (!stripe || !elements) {
      log({
        aggregateId: 'update-billing',
        event: 'quit-no-stripe-elements',
      });

      return;
    }
    let isValid = true;

    for (const status of [cvcStatus, expiryStatus, cardStatus]) {
      if (!status.complete) {
        form.setError(status.name, { message: 'This field is required' });
        isValid = false;
      }
      if (status.error) {
        form.setError(status.name, { message: status.error.message });
        isValid = false;
      }
    }

    if (!isValid) {
      log({
        aggregateId: 'update-billing',
        event: 'form-invalid',
        errors: form.formState.errors,
      });

      return;
    }

    setIsProcessing(true);

    if (!data.billingSameAsShipping) {
      log({
        aggregateId: 'update-billing',
        event: 'updating-billing-address-from-input',
      });
      try {
        await updateBillingAddress({
          address1: data?.address1 ?? '',
          address2: data?.address2 ?? '',
          city: data?.city ?? '',
          state: data?.state ?? '',
          zip: (data?.zip ?? '').toString(),
        });
      } catch (e: unknown) {
        setFormError((e as Error).message);
        setIsProcessing(false);
        log({
          aggregateId: 'update-billing',
          event: 'updating-billing-address-from-input-failed',
          error: e as Error,
        });
        return;
      }
    } else {
      log({
        aggregateId: 'update-billing',
        event: 'updating-billing-address-from-shipping',
      });
      try {
        await updateBillingAddress({
          address1: shippingAddress.address1,
          address2: shippingAddress.address2,
          city: shippingAddress.city,
          state: shippingAddress.state.code,
          zip: shippingAddress.zip,
        });
      } catch (e: unknown) {
        setFormError((e as Error).message);
        setIsProcessing(false);
        log({
          aggregateId: 'update-billing',
          event: 'updating-billing-address-from-shipping-failed',
          error: e as Error,
        });
        return;
      }
    }

    const { error, setupIntent } = await stripe.confirmCardSetup(clientSecret, {
      payment_method: {
        card: cardNumber!,
      },
    });

    if (error) {
      log({
        aggregateId: 'update-billing',
        event: 'updating-billing-stripe-error',
        data: {
          errorType: error.type,
          error: error.message,
        },
      });

      setIsProcessing(false);
      if (error.type === 'card_error' || error.type === 'validation_error') {
        setFormError(error.message);
      } else {
        setFormError('An unexpected error occurred.');
      }
      setIsProcessing(false);
      return;
    }

    log({
      aggregateId: 'update-billing',
      event: 'updating-billing-stripe-done',
      data: {
        setupIntent: setupIntent ?? null,
      },
    });

    await sleep(3000);
    await refreshProfile();

    setIsProcessing(false);
    setIsEditing(false);
    setIsRenderSuccessMessage(true);
  };

  if (isRenderSuccessMessage) return <SuccessMessage />;

  return (
    <div>
      <div className="relative mb-4 flex flex-col gap-10 rounded-2xl bg-stone-light p-4 md:px-6 md:py-8">
        <Loader
          className={cn(
            'absolute left-0 top-0 z-50 hidden h-full w-full rounded-2xl bg-slate-200 opacity-80',
            {
              flex: isEditing && isProcessing,
            },
          )}
        />
        <div className="flex w-full flex-col items-start justify-start gap-5">
          <div className="shrink-0">
            <div className="text-2xl text-denim md:text-lg">Payment Method</div>
            <div className="text-base font-medium text-denim/70">
              Credit or Debit card
            </div>
          </div>

          <div className="flex flex-wrap gap-4 md:gap-4">
            <Visa />
            <Mastercard />
            <Discover />
            <Amex />
            <JCB />
            <FSA />
            <HSA />
          </div>
        </div>
        <ReactForm {...form}>
          <form
            id="payment-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex w-full flex-col gap-3 md:gap-5"
          >
            <div className="w-full">
              <div className="pb-1 text-base font-medium text-denim">
                Card number
              </div>
              <div
                className={cn(
                  'h-[58px] w-full items-center justify-start rounded-lg border border-[#2f4c78] bg-stone-light py-4 pl-4 pr-8 text-xl font-medium text-denim/70',
                  { 'bg-white text-denim': isEditing },
                )}
              >
                {isEditing ? (
                  <CardNumberElement
                    id="cardNumber"
                    className="w-full"
                    onReady={(element) =>
                      onStripeElementReady('card', element, setCardStatus)
                    }
                    options={{
                      placeholder: '0000 0000 0000 0000',
                      style: stripeInputStyle,
                    }}
                  />
                ) : (
                  `**** **** **** ${profile?.paymentMethod?.data?.card?.last4 ?? '****'}`
                )}
              </div>
              <FormField
                control={form.control}
                name={'card'}
                render={() => <FormItem>{renderFormMessage()}</FormItem>}
              />
            </div>
            <div className="flex w-full flex-col gap-3 md:flex-row md:gap-5">
              <div className="w-full">
                <div className="pb-1 text-base font-medium text-denim">
                  {' '}
                  Expiration date
                </div>

                <div
                  className={cn(
                    'h-[58px] w-full items-center justify-start rounded-lg border border-[#2f4c78] bg-stone-light py-4 pl-4 pr-8 text-xl font-medium text-denim/70',
                    { 'bg-white text-denim': isEditing },
                  )}
                >
                  {isEditing ? (
                    <CardExpiryElement
                      id="cardExpiry"
                      className="w-full"
                      onReady={(element) =>
                        onStripeElementReady('expiry', element, setExpiryStatus)
                      }
                      options={{
                        placeholder: 'MM/YY',
                        style: stripeInputStyle,
                      }}
                    />
                  ) : (
                    `${profile?.paymentMethod?.data?.card?.exp_month ?? 'MM'}/${profile?.paymentMethod?.data?.card?.exp_year ?? 'YY'}`
                  )}
                </div>
                <FormField
                  control={form.control}
                  name={'expiry'}
                  render={() => <FormItem>{renderFormMessage()}</FormItem>}
                />
              </div>
              <div className="w-full">
                <div className="pb-1 text-base font-medium text-denim">
                  {' '}
                  CVC
                </div>

                <div
                  className={cn(
                    'h-[58px] w-full items-center justify-start rounded-lg border border-[#2f4c78] bg-stone-light py-4 pl-4 pr-8 text-xl font-medium text-denim/70',
                    { 'bg-white text-denim': isEditing },
                  )}
                >
                  {isEditing ? (
                    <CardCvcElement
                      id="cardCvc"
                      className="w-full"
                      onReady={(element) =>
                        onStripeElementReady('cvc', element, setCvcStatus)
                      }
                      options={{ placeholder: 'CVC', style: stripeInputStyle }}
                    />
                  ) : (
                    '***'
                  )}
                </div>
                <FormField
                  control={form.control}
                  name={'cvc'}
                  render={() => <FormItem>{renderFormMessage()}</FormItem>}
                />
              </div>
            </div>
            <FormField
              control={form.control}
              name={'billingSameAsShipping'}
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className="flex items-start">
                      <Checkbox
                        id="set-billing-address"
                        checked={form.watch('billingSameAsShipping')}
                        disabled={!isEditing}
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                          if (typeof checked === 'boolean')
                            form.setValue('billingSameAsShipping', checked);
                        }}
                        className="mr-4 h-5 w-5 border-none bg-denim/10 data-[state=checked]:bg-denim/10 data-[state=checked]:text-denim md:mt-px"
                      />
                      <FormLabel
                        htmlFor="set-billing-address"
                        className="text-sm font-medium leading-none text-denim md:text-lg"
                      >
                        My billing address is the same as my shipping address.
                      </FormLabel>
                    </div>
                  </FormControl>
                  {renderFormMessage()}
                </FormItem>
              )}
            />
            {!form.getValues('billingSameAsShipping') && (
              <div>
                <FormField
                  control={form.control}
                  name={'address1'}
                  render={({ field }) => (
                    <FormItem>
                      <div className="grid w-full gap-1 py-2">
                        <div className="login-input relative flex w-full items-center">
                          <FormControl>
                            <Input
                              className="disabled:bg-stone-light disabled:text-denim/70"
                              variant="denimOutline"
                              {...field}
                              type="text"
                              disabled={!isEditing}
                              placeholder="Street address"
                            />
                          </FormControl>
                        </div>
                        {renderFormMessage()}
                      </div>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={'address2'}
                  render={({ field }) => (
                    <FormItem>
                      <div className="grid w-full gap-1 py-2">
                        <div className="login-input relative flex w-full items-center">
                          <FormControl>
                            <Input
                              className="disabled:bg-stone-light disabled:text-denim/70"
                              {...field}
                              disabled={!isEditing}
                              variant="denimOutline"
                              type="text"
                              placeholder="Apartment/Suite"
                            />
                          </FormControl>
                        </div>
                        {renderFormMessage()}
                      </div>
                    </FormItem>
                  )}
                />

                <div className="flex w-full flex-col md:flex-row md:gap-5">
                  <FormField
                    control={form.control}
                    name={'city'}
                    render={({ field }) => (
                      <FormItem className="basis-full">
                        <div className="grid w-full gap-1 py-2">
                          <div className="login-input relative flex w-full items-center">
                            <FormControl>
                              <Input
                                className="disabled:bg-stone-light disabled:text-denim/70"
                                variant="denimOutline"
                                disabled={!isEditing}
                                {...field}
                                type="text"
                                placeholder="City"
                              />
                            </FormControl>
                          </div>
                          {renderFormMessage()}
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={'state'}
                    render={({ field }) => (
                      <FormItem className="basis-full">
                        <div className="grid w-full gap-1 py-2">
                          <div className="login-input relative flex w-full items-center">
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                                disabled={!isEditing}
                              >
                                <SelectTrigger
                                  className={
                                    'h-16 w-full rounded-xl !border border-solid border-denim bg-white px-4 py-4 text-xl font-normal text-denim focus-visible:outline-none focus-visible:ring-2 disabled:bg-stone-light disabled:text-denim/70'
                                  }
                                >
                                  <SelectValue placeholder="Select your State" />
                                </SelectTrigger>
                                <SelectContent>
                                  {states.map((state) => (
                                    <SelectItem
                                      key={state.code}
                                      value={state.code}
                                    >
                                      {state.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                          </div>
                          {renderFormMessage()}
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name={'zip'}
                  render={({ field }) => (
                    <FormItem>
                      <div className="grid w-full gap-1 py-2">
                        <div className="login-input relative flex w-full items-center">
                          <FormControl>
                            <Input
                              className="disabled:bg-stone-light disabled:text-denim/70"
                              {...field}
                              disabled={!isEditing}
                              type="text"
                              variant="denimOutline"
                              placeholder="Zip code"
                            />
                          </FormControl>
                        </div>
                        {renderFormMessage()}
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            )}
            {formError && <div className={'text-red-500'}>{formError}</div>}
            {!isEditing ? (
              <div className={'flex items-center gap-10'}>
                <Button
                  className="w-full rounded-xl border text-denim md:w-[200px]"
                  size="md"
                  variant="denimOutline"
                  onClick={(e) => {
                    e.preventDefault();
                    setIsEditing(true);
                  }}
                  type="button"
                >
                  EDIT
                </Button>
              </div>
            ) : (
              <div className={'flex flex-col items-center gap-4 md:flex-row'}>
                <Button
                  size="md"
                  variant="denimOutline"
                  type="button"
                  onClick={() => {
                    setIsEditing(false);
                    setFormError(undefined);
                    form.reset();
                    form.setValue(
                      'billingSameAsShipping',
                      isDefaultBillingSameAsShipping,
                    );
                  }}
                  className="w-full rounded-xl border text-denim md:w-[200px]"
                >
                  CANCEL
                </Button>
                <Button
                  size="md"
                  variant="denim"
                  className="w-full rounded-xl text-white md:w-[200px]"
                  type="submit"
                  disabled={isProcessing || !stripe || !elements}
                >
                  <span>{isProcessing ? 'Submitting' : 'SAVE'}</span>
                </Button>
              </div>
            )}
          </form>
        </ReactForm>
      </div>
    </div>
  );
};

function SuccessMessage() {
  const router = useRouter();

  return (
    <div className="mb-4 flex min-h-[450px] flex-col items-center justify-center gap-6 rounded-2xl bg-stone-light p-6 md:px-6 md:py-8">
      <div className="mx-auto">
        <Image alt="logo" src={Logo} />
      </div>
      <div className="text-2xl font-medium text-denim">Done!</div>
      <div className="flex flex-col gap-2">
        <div className="text-pretty px-4 text-center text-base font-normal text-denim">
          You have successfully updated your billing information. It may take a
          few minutes for the changes to be reflected.
        </div>
      </div>

      <Button
        onClick={() => {
          router.push('/dashboard');
        }}
        variant={'denim'}
        className="w-fit justify-self-center px-12 py-6 text-lg font-semibold text-white"
        type="button"
        size={'sm'}
      >
        Home
        <CircleArrowRight />
      </Button>
    </div>
  );
}
