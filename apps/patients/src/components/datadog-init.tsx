'use client';

import { datadogLogs } from '@datadog/browser-logs';

// import { datadogRum } from '@datadog/browser-rum';

import { env } from '~/env';

if (!['development', 'test'].includes(env.NEXT_PUBLIC_ENVIRONMENT)) {
  // datadogRum.init({
  //   applicationId: '773c4347-71bf-45e1-8d52-ad12b0242f94',
  //   clientToken: 'pub0fe048cc3822f28b429d1a1f09049bfb',
  //   site: 'us5.datadoghq.com',
  //   service: 'patient-dashboard',
  //   env: env.NEXT_PUBLIC_ENVIRONMENT || 'development',
  //   // version: '1.0.0',
  //   // sessionSampleRate: 100,
  //   sessionReplaySampleRate:
  //     env.NEXT_PUBLIC_ENVIRONMENT == 'production' ? 100 : 0,
  //   trackUserInteractions: true,
  //   trackResources: true,
  //   trackLongTasks: true,
  //   defaultPrivacyLevel: 'mask-user-input',
  //   // Specify URLs to propagate trace headers for connection between RUM and backend trace
  //   allowedTracingUrls: [
  //     {
  //       match: 'https://api-staging.startwillow.com/',
  //       propagatorTypes: ['tracecontext'],
  //     },
  //     {
  //       match: 'https://api.startwillow.com/',
  //       propagatorTypes: ['tracecontext'],
  //     },
  //   ],
  //   proxy: (options) => `/dd${options.path}/foo?${options.parameters}`,
  // });

  datadogLogs.init({
    clientToken: 'pub0fe048cc3822f28b429d1a1f09049bfb',
    site: 'us5.datadoghq.com',
    service: 'patient-dashboard',
    env: env.NEXT_PUBLIC_ENVIRONMENT || 'development',
    forwardErrorsToLogs: true,
    // sessionSampleRate: 100,
    proxy: (options) => `/dd${options.path}/foo?${options.parameters}`,
  });
}

export default function DatadogInit() {
  // Render nothing - this component is only included so that the init code
  // above will run client-side
  return null;
}
