'use client';

import type {
  Analytics,
  Callback,
  EventProperties,
  ID,
  SegmentEvent,
  UserTraits,
} from '@segment/analytics-next';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { useAnalyticsProperties } from '@/hooks/useAnalyticsProperties';
import { getDeviceDetailsForContext } from '@/lib/utm-capture';
import { AnalyticsBrowser } from '@segment/analytics-next';
import mixpanel from 'mixpanel-browser';

import { env } from '~/env';

const AnalyticsContext = createContext<Analytics | null>(null);

const UniversalScriptComponent = () => {
  useEffect(() => {
    const head = document.head;
    const script = document.createElement('script');

    script.type = 'text/javascript';
    script.src =
      'https://t.startwillow.com/v1/lst/universal-script?ph=d2a60b67d55cdb1fcec727a9ab9acd784e0e178a29374a8a51b8b1329e6217fd&tag=!clicked&ref_url=' +
      encodeURIComponent(document.URL);

    head.appendChild(script);

    // Cleanup to remove the script when the component is unmounted
    return () => {
      head.removeChild(script);
    };
  }, []); // Empty dependency array ensures this effect runs only once

  return null; // Component returns nothing visible
};

export const AnalyticsProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const analyticsProperties = useAnalyticsProperties();
  const deviceContext = getDeviceDetailsForContext();
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const pathname = usePathname();
  useEffect(() => {
    const loadAnalytics = async () => {
      if (!analytics) {
        const [analyticsInstance] = await AnalyticsBrowser.load({
          writeKey: env.NEXT_PUBLIC_SEGMENT_WRITE_KEY,
        });
        setAnalytics(analyticsInstance);
      }
    };

    void loadAnalytics();
  }, [analytics]);

  useEffect(() => {
    if (analytics) {
      void analytics.page(deviceContext, analyticsProperties);
    }
  }, [analytics, pathname]);

  useEffect(() => {
    if (!analytics) return;
    if (env.NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN) {
      console.log('Initializing Mixpanel');
      mixpanel.init(env.NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN, {
        autocapture: false,
        record_sessions_percent: 2,
        api_host: `${env.NEXT_PUBLIC_PATIENTS_URL}/mp`,
      });
    }
  }, [analytics]);

  return (
    <>
      <AnalyticsContext.Provider value={analytics}>
        {children}
      </AnalyticsContext.Provider>
      <UniversalScriptComponent />
    </>
  );
};

export const useAnalytics = () => {
  const deviceContext = getDeviceDetailsForContext();
  const analyticsProperties = useAnalyticsProperties();
  const analytics = useContext(AnalyticsContext);

  const track = (
    eventName: string | SegmentEvent,
    properties?: EventProperties,
  ) => {
    const eventProperties = { ...properties, ...deviceContext };
    const eventOptions: any = {
      context: {
        ...analyticsProperties,
        campaign: deviceContext.campaign,
      },
    };
    return analytics?.track(eventName, eventProperties, eventOptions);
  };

  const identify = (
    id?: object | ID,
    traits?: UserTraits | Callback | null,
  ) => {
    void analytics?.identify(id, traits);
    if (env.NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN && typeof id === 'string') {
      mixpanel.identify(id);
    }
  };

  if (analytics) {
    return {
      identify,
      track,
    };
  } else {
    return null;
  }
};
