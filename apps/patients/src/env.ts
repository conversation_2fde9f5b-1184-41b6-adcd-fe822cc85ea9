/* eslint-disable no-restricted-properties */
import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const env = createEnv({
  shared: {
    NODE_ENV: z
      .enum(['development', 'production', 'test'])
      .default('development'),
  },
  /**
   * Specify your server-side environment variables schema here.
   * This way you can ensure the app isn't built with invalid env vars.
   */
  server: {},

  /**
   * Specify your client-side environment variables schema here.
   * For them to be exposed to the client, prefix them with `NEXT_PUBLIC_`.
   */
  client: {
    NEXT_PUBLIC_ENVIRONMENT: z
      .enum(['development', 'staging', 'production', 'test'])
      .default('development'),
    NEXT_PUBLIC_API_URL: z.string().url(),
    NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL: z.string().url(),
    NEXT_PUBLIC_API_S3_URL: z.string().url(),
    NEXT_PUBLIC_API_STRIPE_PUBLISHABLE_KEY: z.string(),
    NEXT_PUBLIC_SEGMENT_WRITE_KEY: z.string(),
    NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: z.string(),
    NEXT_PUBLIC_PATIENTS_URL: z.string().url(),
    NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN: z.string().optional(),
  },
  /**
   * Destructure all variables from `process.env` to make sure they aren't tree-shaken away.
   */
  experimental__runtimeEnv: {
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_ENVIRONMENT: process.env.NEXT_PUBLIC_ENVIRONMENT,
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL:
      process.env.NEXT_PUBLIC_API_PATIENTS_PHOTOS_URL,
    NEXT_PUBLIC_API_S3_URL: process.env.NEXT_PUBLIC_API_S3_URL,
    NEXT_PUBLIC_API_STRIPE_PUBLISHABLE_KEY:
      process.env.NEXT_PUBLIC_API_STRIPE_PUBLISHABLE_KEY,
    NEXT_PUBLIC_SEGMENT_WRITE_KEY: process.env.NEXT_PUBLIC_SEGMENT_WRITE_KEY,
    NEXT_PUBLIC_GOOGLE_MAPS_API_KEY:
      process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
    NEXT_PUBLIC_PATIENTS_URL: process.env.NEXT_PUBLIC_PATIENTS_URL,
    NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN:
      process.env.NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN,
  },
  skipValidation:
    !!process.env.CI || process.env.npm_lifecycle_event === 'lint',
});
