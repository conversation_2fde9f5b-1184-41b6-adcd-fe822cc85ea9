'use client';

import { useRef } from 'react';
import { datadogLogs } from '@datadog/browser-logs';

import { env } from '~/env';
import { usePatientStatus } from './profile';

type LogSeverity = 'info' | 'error' | 'warn' | 'debug';

interface LogPayload {
  severity?: LogSeverity;
  event: string;
  aggregateId?: string;
  traceId?: string;
  error?: Error;
  [key: string]: unknown;
}

export function useLog() {
  const { data: profile } = usePatientStatus();

  const logRef = useRef(
    ({ event, aggregateId, severity, error, ...payload }: LogPayload) => {
      if (env.NEXT_PUBLIC_ENVIRONMENT === 'development') {
        console.log(
          `[${severity}] Logging: ${aggregateId ? `${aggregateId}->` : ''}${event}`,
          payload,
        );
        return;
      }

      void datadogLogs.logger.log(
        event,
        {
          source: 'patient-dashboard-logs',
          session: profile ? { email: profile?.user.email } : undefined,
          aggregateId,
          app: 'patient-dashboard',

          ...payload,
        },
        severity ?? 'info',
        error,
      );
    },
  );

  return {
    genRandomTraceId: () => Math.random().toString(36).substring(2, 15),
    log: logRef.current,
    initTracedLog: (_tracingId?: string) => {
      const tracingId =
        _tracingId ?? Math.random().toString(36).substring(2, 15);
      return (data: LogPayload) => logRef.current({ tracingId, ...data });
    },
    isPending: false,
  };
}
