{"name": "@willow/patients", "version": "0.1.0", "private": true, "scripts": {"purge": "git clean -xdf .cache .turbo .next node_modules", "clean": "git clean -xdf .cache .turbo .next", "build": "pnpm with-env next build", "dev": "pnpm with-env bash -c 'next dev -p ${PATIENTS_PORT:-3000}'", "start": "pnpm with-env next start -p ${PATIENTS_PORT:-3000}", "format": "prettier --check . --ignore-path ../../.gitignore", "format:fix": "prettier --check --write . --ignore-path ../../.gitignore", "lint": "eslint", "lint:fix": "eslint --fix", "with-env": "dotenv -e ../../.env -e ../../.env.local --"}, "prettier": "@willow/prettier-config", "dependencies": {"@datadog/browser-logs": "^6.0.0", "@datadog/browser-rum": "^6.0.0", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@react-google-maps/api": "^2.19.3", "@react-input/mask": "^1.0.21", "@segment/analytics-next": "^1.64.0", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "@tanstack/react-query": "catalog:", "@uidotdev/usehooks": "^2.4.1", "@willow/chat": "workspace:*", "@willow/ui": "workspace:*", "@willow/utils": "workspace:*", "@xstate/react": "^4.0.3", "axios": "^1.7.9", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "device-detector-js": "^3.0.3", "jotai": "^2.10.3", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.453.0", "mixpanel-browser": "^2.69.1", "mobile-device-detect": "^0.4.3", "next": "^14.2.15", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.52.2", "react-image-file-resizer": "^0.4.8", "react-use-measure": "^2.1.1", "sharp": "^0.33.2", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "xstate": "^5.18.1", "zod": "catalog:"}, "devDependencies": {"@tanstack/react-query-devtools": "catalog:", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.7", "@types/node": "^22.5.0", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@willow/eslint-config": "workspace:*", "@willow/prettier-config": "workspace:*", "@willow/tailwind-config": "workspace:*", "@willow/tsconfig": "workspace:*", "autoprefixer": "^10.0.1", "dotenv-cli": "^7.4.2", "eslint": "catalog:", "jiti": "^2.3.3", "postcss": "^8.4.41", "prettier": "catalog:", "tailwindcss": "^3.4.4", "typescript": "catalog:"}}